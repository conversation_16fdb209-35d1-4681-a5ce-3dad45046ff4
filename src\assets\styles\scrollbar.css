/**
 * 全局滚动条美化样式
 * 现代化设计，支持浅色和深色主题
 */

/* ===== 防止外层滚动条样式 ===== */
html, body {
  height: 100%;
  overflow: hidden; /* 防止页面级别的滚动条 */
}

#app {
  height: 100%;
  overflow: hidden; /* 防止应用级别的滚动条 */
}

.v-application {
  height: 100vh !important;
  overflow: hidden !important; /* 防止Vuetify应用的滚动条 */
}

.v-main {
  height: 100vh !important;
  overflow: hidden !important; /* 防止主内容区域的滚动条 */
}

/* ===== 全局滚动条基础样式 ===== */
:root {
  /* 滚动条颜色变量 - 浅色主题 */
  --scrollbar-width: 8px;
  --scrollbar-track-bg: transparent;
  --scrollbar-thumb-bg: rgba(0, 0, 0, 0.1);
  --scrollbar-thumb-hover-bg: rgba(0, 0, 0, 0.2);
  --scrollbar-thumb-active-bg: rgba(0, 0, 0, 0.3);
  --scrollbar-border-radius: 6px;
  
  /* 渐变色定义 */
  --scrollbar-gradient: linear-gradient(135deg, rgba(0, 0, 0, 0.08) 0%, rgba(0, 0, 0, 0.12) 100%);
  --scrollbar-gradient-hover: linear-gradient(135deg, rgba(0, 0, 0, 0.15) 0%, rgba(0, 0, 0, 0.25) 100%);
  --scrollbar-gradient-active: linear-gradient(135deg, rgba(0, 0, 0, 0.25) 0%, rgba(0, 0, 0, 0.35) 100%);
}

/* 深色主题滚动条变量 */
[data-theme="dark"] {
  --scrollbar-thumb-bg: rgba(255, 255, 255, 0.15);
  --scrollbar-thumb-hover-bg: rgba(255, 255, 255, 0.25);
  --scrollbar-thumb-active-bg: rgba(255, 255, 255, 0.35);
  --scrollbar-gradient: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.2) 100%);
  --scrollbar-gradient-hover: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.3) 100%);
  --scrollbar-gradient-active: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.4) 100%);
}

/* ===== Webkit 浏览器滚动条样式 (Chrome, Safari, Edge) ===== */
* {
  /* 滚动条整体样式 */
  scrollbar-width: thin;
  scrollbar-color: var(--scrollbar-thumb-bg) var(--scrollbar-track-bg);
}

*::-webkit-scrollbar {
  width: var(--scrollbar-width);
  height: var(--scrollbar-width);
}

*::-webkit-scrollbar-track {
  background: var(--scrollbar-track-bg);
  border-radius: var(--scrollbar-border-radius);
}

*::-webkit-scrollbar-thumb {
  background: var(--scrollbar-gradient);
  border-radius: var(--scrollbar-border-radius);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

*::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-gradient-hover);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: scale(1.1);
}

*::-webkit-scrollbar-thumb:active {
  background: var(--scrollbar-gradient-active);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transform: scale(0.95);
}

/* 滚动条角落样式 */
*::-webkit-scrollbar-corner {
  background: var(--scrollbar-track-bg);
}

/* ===== 特殊区域滚动条样式 ===== */

/* 聊天消息区域滚动条 - 更加突出 */
.chat-messages::-webkit-scrollbar {
  width: 10px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
  transform: scale(1.05);
}

.chat-messages::-webkit-scrollbar-thumb:active {
  background: linear-gradient(135deg, #4e5bc6 0%, #5e377e 100%);
  transform: scale(0.98);
}

/* 侧边栏滚动条 - 更加精致 */
.sidebar::-webkit-scrollbar,
.conversation-history::-webkit-scrollbar {
  width: 6px;
}

.sidebar::-webkit-scrollbar-thumb,
.conversation-history::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.2) 100%);
  border-radius: 4px;
  transition: all 0.2s ease;
}

.sidebar::-webkit-scrollbar-thumb:hover,
.conversation-history::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0.3) 100%);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
  :root {
    --scrollbar-width: 6px;
    --scrollbar-border-radius: 4px;
  }
  
  .chat-messages::-webkit-scrollbar {
    width: 8px;
  }
  
  .sidebar::-webkit-scrollbar,
  .conversation-history::-webkit-scrollbar {
    width: 4px;
  }
}

@media (max-width: 480px) {
  :root {
    --scrollbar-width: 4px;
    --scrollbar-border-radius: 3px;
  }
  
  .chat-messages::-webkit-scrollbar {
    width: 6px;
  }
}

/* ===== 特殊效果 ===== */

/* 滚动时的动画效果 */
@keyframes scrollbar-appear {
  from {
    opacity: 0;
    transform: scaleY(0.5);
  }
  to {
    opacity: 1;
    transform: scaleY(1);
  }
}

/* 当容器被悬停时显示滚动条 */
.scrollbar-on-hover::-webkit-scrollbar-thumb {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.scrollbar-on-hover:hover::-webkit-scrollbar-thumb {
  opacity: 1;
  animation: scrollbar-appear 0.3s ease;
}

/* 彩虹滚动条效果（可选） */
.rainbow-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, 
    #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #ffeaa7, #dda0dd, #98d8c8);
  background-size: 400% 400%;
  animation: rainbow-scroll 3s ease infinite;
}

@keyframes rainbow-scroll {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* 毛玻璃效果滚动条 */
.glass-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.glass-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}
